<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 text-gray-800 overflow-x-hidden" @mousemove="handleMouseMove">
    <!-- 粒子背景系统 -->
    <div id="particles-background" class="fixed inset-0 z-0"></div>

    <!-- 动态渐变背景 -->
    <div class="dynamic-gradient-bg"></div>

    <!-- 跟随鼠标的蓝色圆点 -->
    <div class="mouse-follower" :style="mouseFollowerStyle"></div>

    <!-- 滚动进度指示器 -->
    <div class="scroll-progress" :style="{ width: scrollProgress + '%' }"></div>

    <!-- Hero Section -->
    <section
      class="relative h-screen flex items-center justify-center overflow-hidden"
    >
      <div class="absolute inset-0 z-0">
        <img
          src="https://ai-public.mastergo.com/ai/img_res/5066ff6207b2fe945fd04e7852b7d616.jpg"
          alt="Background"
          class="w-full h-full object-cover object-center opacity-80"
        />
      </div>
      <div class="relative z-10 text-center px-4">
        <!-- <img src="https://ai-public.mastergo.com/ai/img_res/9db5799e872b53e151928a0dc4c312df.jpg" alt="Logo" class="w-32 h-32 mx-auto mb-6"> -->
        <h1 class="text-7xl font-bold mb-10 bg-gradient-to-r from-cyan-200 via-blue-300 to-teal-300 bg-clip-text text-transparent tracking-tight typewriter-container">
          <span class="typewriter-text">{{ displayedTitle }}</span>
          <span class="typewriter-cursor" :class="{ 'cursor-hidden': titleComplete }">|</span>
        </h1>
        <p class="text-2xl mb-8 text-white font-medium leading-relaxed tracking-wide fade-in-up drop-shadow-lg" :class="{ 'animate-fade-in': titleComplete }">大模型赋能，精准检测，为您的车载网络保驾护航</p>
        <!-- 在 template 中 -->
        <el-button
          type="primary"
          class="custom-button text-white font-bold py-4 px-16 transition-all duration-300 text-lg relative overflow-hidden ripple-button"
          @click="handleButtonClick"
        >
          立即体验
          <span class="ripple" v-for="ripple in ripples" :key="ripple.id" :style="ripple.style"></span>
        </el-button>

        <!-- <button @click="goToLogin" class="bg-[#4ED9F5] text-gray-900 px-8 py-3 rounded-full text-lg font-semibold hover:bg-[#3bc8e4] transition-colors duration-300 !rounded-button whitespace-nowrap get-start-button">立即体验</button> -->
      </div>
      <div
        class="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce"
      >
        <!-- <i class="fas fa-angle-down text-[#4ED9F5] text-3xl"></i> -->
        <font-awesome-icon
          @click="scrollToCallToAction"
          icon="fa-solid fa-angle-down"
          class="text-[#4ED9F5] text-3xl hover:text-[#3bc8e6] transition-colors duration-300 cursor-pointer animate-bounce"
        />
      </div>
    </section>

    <!-- 图片与下方内容的衔接 -->
    <div class="wave-transition">
      <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="wave-svg">
        <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" class="wave-fill"></path>
        <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" class="wave-fill"></path>
        <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" class="wave-fill"></path>
      </svg>
    </div>

    <!-- Platform Advantages -->
    <section class="py-20 px-4 bg-white relative">
      <!-- 动态背景效果 -->
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
      </div>
      <h1 class="text-4xl font-bold text-center mb-12 bg-gradient-to-r from-[#4ED9F5] to-[#3bc8e6] bg-clip-text text-transparent tracking-tight">
        平台优势
      </h1>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        <div
          v-for="(advantage, index) in advantages"
          :key="index"
          class="advantage-card"
          @mouseenter="handleCardHover(index, true)"
          @mouseleave="handleCardHover(index, false)"
          :class="{ 'card-hovered': hoveredCard === index }"
          :style="{ animationDelay: `${index * 0.2}s` }"
        >
          <div class="card-content">
            <div class="icon-wrapper">
              <div class="icon-background"></div>
              <div class="icon-pulse"></div>
              <i :class="advantage.icon + ' card-icon'"></i>
            </div>
            <h3 class="card-title">
              {{ advantage.title }}
            </h3>
            <p class="card-description">
              {{ advantage.shortDescription }}
            </p>


          </div>
        </div>
      </div>
    </section>
    <!-- Call to Action -->
    <section ref="callToAction" class="py-20 px-4 text-center bg-gradient-to-b from-white to-blue-50">
      <h1 class="text-4xl font-bold mb-6 text-[#4ED9F5]">
        开始使用智能车载系统安全卫士
      </h1>
      <p class="text-xl mb-8 text-gray-700">
        立即体验大模型赋能的模糊测试，提升您的车载网络安全
      </p>
      <button
        @click="goToLogin"
        class="bg-[#4ED9F5] text-white px-12 py-4 rounded-full text-xl font-semibold hover:bg-[#3EAEC2] transition-all duration-300 transform hover:scale-105 !rounded-button whitespace-nowrap shadow-lg hover:shadow-xl"
      >
        开始使用
      </button>
    </section>

    <!-- 底部卡通小车动画 -->
    <div class="cars-transition-bottom">
      <!-- 天空装饰云朵 -->
      <div class="sky-decorations">
        <div class="cloud cloud-1"></div>
        <div class="cloud cloud-2"></div>
        <div class="cloud cloud-3"></div>
      </div>

      <!-- 道路背景 -->
      <div class="road-background"></div>

      <!-- 移动的小车 -->
      <div class="car car-1">
        <div class="car-body">
          <div class="car-roof"></div>
          <div class="car-window"></div>
          <div class="car-door"></div>
          <div class="car-headlight"></div>
          <div class="car-taillight"></div>
        </div>
        <div class="car-wheels">
          <div class="wheel wheel-front"></div>
          <div class="wheel wheel-rear"></div>
        </div>
        <div class="car-exhaust">
          <div class="exhaust-smoke"></div>
          <div class="exhaust-smoke"></div>
          <div class="exhaust-smoke"></div>
        </div>
      </div>

      <div class="car car-2">
        <div class="car-body">
          <div class="car-roof"></div>
          <div class="car-window"></div>
          <div class="car-door"></div>
          <div class="car-headlight"></div>
          <div class="car-taillight"></div>
        </div>
        <div class="car-wheels">
          <div class="wheel wheel-front"></div>
          <div class="wheel wheel-rear"></div>
        </div>
        <div class="car-exhaust">
          <div class="exhaust-smoke"></div>
          <div class="exhaust-smoke"></div>
          <div class="exhaust-smoke"></div>
        </div>
      </div>

      <div class="car car-3">
        <div class="car-body">
          <div class="car-roof"></div>
          <div class="car-window"></div>
          <div class="car-door"></div>
          <div class="car-headlight"></div>
          <div class="car-taillight"></div>
        </div>
        <div class="car-wheels">
          <div class="wheel wheel-front"></div>
          <div class="wheel wheel-rear"></div>
        </div>
        <div class="car-exhaust">
          <div class="exhaust-smoke"></div>
          <div class="exhaust-smoke"></div>
          <div class="exhaust-smoke"></div>
        </div>
      </div>

      <!-- 道路标线 -->
      <div class="road-lines">
        <div class="road-line"></div>
        <div class="road-line"></div>
        <div class="road-line"></div>
        <div class="road-line"></div>
        <div class="road-line"></div>
      </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white py-8 px-4 border-t border-gray-200">
      <div
        class="max-w-6xl mx-auto flex flex-col md:flex-row justify-between items-center"
      >
        <div class="text-gray-600 mb-4 md:mb-0">
          © 2025 智能车载系统卫士. 保留所有权利.
        </div>
        <div class="flex space-x-4">
          <a
            href="#"
            class="text-gray-600 hover:text-[#4ED9F5] transition-colors duration-300"
            >隐私政策</a
          >
          <a
            href="#"
            class="text-gray-600 hover:text-[#4ED9F5] transition-colors duration-300"
            >使用条款</a
          >
          <a
            href="#"
            class="text-gray-600 hover:text-[#4ED9F5] transition-colors duration-300"
            >联系我们</a
          >
        </div>
      </div>
    </footer>



  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick, reactive, onUnmounted } from "vue";
import router from "../router";

const advantages = [
  {
    icon: "fas fa-brain",
    title: "智能分析",
    shortDescription: "利用大模型技术，智能识别潜在威胁，提高检测准确率",
    description: "基于先进的大模型技术，深度学习车载网络行为模式，智能识别异常流量和潜在威胁，显著提升安全检测的准确率和效率。通过机器学习算法持续优化检测模型，实现对未知威胁的预测性防护。",
    features: ["AI 驱动分析", "深度学习算法", "智能威胁识别", "自动化分析"],
    details: {
      accuracy: "99.5%",
      speed: "毫秒级响应",
      coverage: "全协议支持"
    }
  },
  {
    icon: "fas fa-shield-alt",
    title: "全面防护",
    shortDescription: "覆盖车载网络各个层面，为您的车辆提供全方位安全保障",
    description: "构建多层次、全方位的车载网络安全防护体系，从物理层到应用层全面覆盖，为您的智能车辆提供企业级安全保障。集成入侵检测、防火墙、访问控制等多种安全机制，形成立体化防护网络。",
    features: ["多层次防护", "实时监控预警", "全网络覆盖", "主动防御机制"],
    details: {
      layers: "7层防护",
      monitoring: "24/7监控",
      response: "秒级响应"
    }
  },
  {
    icon: "fas fa-tachometer-alt",
    title: "高效检测",
    shortDescription: "快速扫描和分析，及时发现并报告安全隐患",
    description: "采用高性能检测引擎，实现快速扫描和实时分析，能够在最短时间内发现安全隐患并生成详细的安全报告。支持并行处理和分布式检测，大幅提升检测效率和覆盖范围。",
    features: ["高速扫描引擎", "实时威胁检测", "精准漏洞定位", "智能报告生成"],
    details: {
      scanSpeed: "10Gbps",
      detection: "实时检测",
      reporting: "自动生成"
    }
  },
];

const chartContainer = ref<HTMLElement | null>(null);

// 打字机效果
const displayedTitle = ref('');
const titleComplete = ref(false);
const fullTitle = '智能车载系统安全卫士';

// 滚动进度
const scrollProgress = ref(0);

// 卡片悬停状态
const hoveredCard = ref(-1);

// 鼠标跟随效果
const mouseFollowerStyle = ref({
  left: '0px',
  top: '0px',
  transform: 'translate(-50%, -50%)',
  opacity: '0'
});



// 波纹效果
const ripples = ref([]);
let rippleId = 0;



// 打字机效果函数
const startTypewriter = () => {
  let index = 0;
  const typeInterval = setInterval(() => {
    if (index < fullTitle.length) {
      displayedTitle.value += fullTitle[index];
      index++;
    } else {
      clearInterval(typeInterval);
      titleComplete.value = true;
    }
  }, 150);
};

// 滚动进度处理
const handleScroll = () => {
  const scrollTop = window.pageYOffset;
  const docHeight = document.documentElement.scrollHeight - window.innerHeight;
  const scrollPercent = (scrollTop / docHeight) * 100;
  scrollProgress.value = Math.min(scrollPercent, 100);
};

// 卡片悬停处理
const handleCardHover = (index: number, isHovered: boolean) => {
  hoveredCard.value = isHovered ? index : -1;
};

// 鼠标移动处理函数
const handleMouseMove = (event: MouseEvent) => {
  requestAnimationFrame(() => {
    mouseFollowerStyle.value = {
      left: `${event.clientX}px`,
      top: `${event.clientY}px`,
      transform: 'translate(-50%, -50%)',
      opacity: '1'
    };
  });
};

// 鼠标离开页面时隐藏圆点
const handleMouseLeave = () => {
  mouseFollowerStyle.value.opacity = '0';
};

// 鼠标进入页面时显示圆点
const handleMouseEnter = () => {
  mouseFollowerStyle.value.opacity = '1';
};



// 波纹效果处理
const handleButtonClick = (event: MouseEvent) => {
  const button = event.currentTarget as HTMLElement;
  const rect = button.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;

  const ripple = {
    id: rippleId++,
    style: {
      left: `${x}px`,
      top: `${y}px`,
      transform: 'translate(-50%, -50%)',
    }
  };

  ripples.value.push(ripple);

  setTimeout(() => {
    const index = ripples.value.findIndex(r => r.id === ripple.id);
    if (index > -1) {
      ripples.value.splice(index, 1);
    }
  }, 600);

  // 延迟导航以显示波纹效果
  setTimeout(() => {
    goToLogin();
  }, 200);
};



// 获取详细信息标签
const getDetailLabel = (key: string) => {
  const labels: { [key: string]: string } = {
    accuracy: '检测精度',
    speed: '响应速度',
    coverage: '协议覆盖',
    layers: '防护层级',
    monitoring: '监控时间',
    response: '响应时间',
    scanSpeed: '扫描速度',
    detection: '检测模式',
    reporting: '报告生成'
  };
  return labels[key] || key;
};

const scrollToCallToAction = () => {
  const element = document.querySelector(".py-20");
  if (element) {
    element.scrollIntoView({ behavior: "smooth" });
  }
};

const goToLogin = () => {
  router.push("/login");
};

onMounted(() => {
  // 启动打字机效果
  setTimeout(() => {
    startTypewriter();
  }, 500);

  // 添加事件监听器
  window.addEventListener('scroll', handleScroll);
  document.addEventListener('mouseleave', handleMouseLeave);
  document.addEventListener('mouseenter', handleMouseEnter);
});

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener('scroll', handleScroll);
  document.removeEventListener('mouseleave', handleMouseLeave);
  document.removeEventListener('mouseenter', handleMouseEnter);
});
</script>

<style scoped>
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700;800;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800;900&display=swap");

/* 全局字体优化 */
* {
  font-family: "Inter", "Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "liga" 1, "kern" 1;
}

body {
  font-family: "Inter", "Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
  letter-spacing: -0.01em;
  line-height: 1.6;
  font-weight: 400;
}

/* 标题字体优化 */
h1, h2, h3, h4, h5, h6 {
  font-family: "Playfair Display", "Montserrat", "Inter", "Noto Sans SC", serif;
  font-weight: 700;
  letter-spacing: -0.02em;
  line-height: 1.2;
}

/* 主标题特殊字体 */
.typewriter-container {
  font-family: "Montserrat", "Playfair Display", "Inter", sans-serif;
  font-weight: 800;
  letter-spacing: -0.03em;
}

/* 数据字体 */
.detail-value, .feature-tag {
  font-family: "JetBrains Mono", "Inter", "Noto Sans SC", monospace;
}

/* 行高优化 */
.line-height-relaxed {
  line-height: 1.7;
}

.swiper-pagination-bullet {
  background-color: #4ED9F5;
}

.swiper-pagination-bullet-active {
  background-color: #3EAEC2;
}

/* 自定义输入框样式 */
input[type="text"],
input[type="email"],
input[type="password"] {
  @apply bg-white border-gray-300 text-gray-700 focus:border-[#4ED9F5] focus:ring-[#4ED9F5];
}

/* 去除number类型输入框的默认箭头 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f5f7fa;
}

::-webkit-scrollbar-thumb {
  background: #4ED9F5;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #3EAEC2;
}

/* 鼠标跟随效果 */
.mouse-follower {
  position: fixed;
  width: 24px;
  height: 24px;
  background: radial-gradient(circle,
    rgba(78, 217, 245, 0.9) 0%,
    rgba(78, 217, 245, 0.6) 30%,
    rgba(78, 217, 245, 0.3) 60%,
    transparent 100%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  transition: opacity 0.3s ease;
  box-shadow:
    0 0 15px rgba(78, 217, 245, 0.8),
    0 0 30px rgba(78, 217, 245, 0.4),
    0 0 45px rgba(78, 217, 245, 0.2);
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
  0% {
    transform: translate(-50%, -50%) scale(1);
    box-shadow:
      0 0 15px rgba(78, 217, 245, 0.8),
      0 0 30px rgba(78, 217, 245, 0.4),
      0 0 45px rgba(78, 217, 245, 0.2);
  }
  100% {
    transform: translate(-50%, -50%) scale(1.1);
    box-shadow:
      0 0 20px rgba(78, 217, 245, 1),
      0 0 40px rgba(78, 217, 245, 0.6),
      0 0 60px rgba(78, 217, 245, 0.3);
  }
}

/* 粒子背景 */
#particles-background {
  pointer-events: none;
}

/* CSS粒子动画 */
.css-particle {
  pointer-events: none;
}

@keyframes float-particle {
  0% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) translateX(50px) rotate(360deg);
    opacity: 0;
  }
}

/* 动态渐变背景 */
.dynamic-gradient-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg,
    rgba(78, 217, 245, 0.1) 0%,
    rgba(59, 200, 230, 0.05) 25%,
    rgba(78, 217, 245, 0.08) 50%,
    rgba(59, 200, 230, 0.03) 75%,
    rgba(78, 217, 245, 0.1) 100%);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  z-index: 1;
  pointer-events: none;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 滚动进度指示器 */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  height: 4px;
  background: linear-gradient(90deg, #4ED9F5, #3bc8e6);
  z-index: 9999;
  transition: width 0.3s ease;
  box-shadow: 0 0 10px rgba(78, 217, 245, 0.5);
}

/* 打字机效果 */
.typewriter-container {
  position: relative;
}

.typewriter-cursor {
  animation: blink 1s infinite;
  color: #4ED9F5;
  transition: opacity 0.5s ease;
}

.typewriter-cursor.cursor-hidden {
  opacity: 0;
  animation: none;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* 淡入动画 */
.fade-in-up {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s ease;
}

.animate-fade-in {
  opacity: 1;
  transform: translateY(0);
}

/* 波纹效果 */
.ripple-button {
  position: relative;
  overflow: hidden;
}

.ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transform: scale(0);
  animation: ripple-animation 0.6s linear;
  pointer-events: none;
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* 在移动设备上隐藏鼠标跟随效果 */
@media (max-width: 768px) {
  .mouse-follower {
    display: none;
  }
}

/* 波浪过渡效果 */
.wave-transition {
  position: relative;
  height: 120px;
  overflow: hidden;
  line-height: 0;
}

.wave-svg {
  position: relative;
  display: block;
  width: calc(100% + 1.3px);
  height: 120px;
}

.wave-fill {
  fill: #ffffff;
}

/* 底部卡通小车动画 */
.cars-transition-bottom {
  position: relative;
  height: 120px;
  background: linear-gradient(180deg,
    #e0f2fe 0%,
    #f0f9ff 20%,
    #f8fafc 50%,
    #f1f5f9 80%,
    #e2e8f0 100%);
  overflow: hidden;
  border-top: 1px solid rgba(78, 217, 245, 0.2);
  box-shadow:
    inset 0 4px 8px rgba(0, 0, 0, 0.03),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.cars-transition-bottom::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(180deg,
    rgba(78, 217, 245, 0.05) 0%,
    rgba(78, 217, 245, 0.02) 50%,
    transparent 100%);
  pointer-events: none;
}

/* 天空装饰 */
.sky-decorations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 80px;
  pointer-events: none;
}

/* 云朵样式 */
.cloud {
  position: absolute;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50px;
  opacity: 0.6;
  animation: cloudFloat 20s linear infinite;
}

.cloud::before,
.cloud::after {
  content: '';
  position: absolute;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50px;
}

.cloud-1 {
  width: 40px;
  height: 15px;
  top: 15px;
  left: -50px;
  animation-delay: 0s;
}

.cloud-1::before {
  width: 20px;
  height: 20px;
  top: -8px;
  left: 5px;
}

.cloud-1::after {
  width: 25px;
  height: 18px;
  top: -6px;
  right: 5px;
}

.cloud-2 {
  width: 30px;
  height: 12px;
  top: 25px;
  left: -40px;
  animation-delay: 8s;
}

.cloud-2::before {
  width: 15px;
  height: 15px;
  top: -6px;
  left: 3px;
}

.cloud-2::after {
  width: 18px;
  height: 14px;
  top: -5px;
  right: 3px;
}

.cloud-3 {
  width: 35px;
  height: 14px;
  top: 10px;
  left: -45px;
  animation-delay: 15s;
}

.cloud-3::before {
  width: 18px;
  height: 18px;
  top: -7px;
  left: 4px;
}

.cloud-3::after {
  width: 22px;
  height: 16px;
  top: -6px;
  right: 4px;
}

@keyframes cloudFloat {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(calc(100vw + 100px));
  }
}

/* 道路背景 */
.road-background {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 40px;
  background: linear-gradient(180deg,
    #52525b 0%,
    #3f3f46 30%,
    #27272a 70%,
    #18181b 100%);
  box-shadow:
    inset 0 3px 6px rgba(0, 0, 0, 0.3),
    inset 0 -1px 2px rgba(255, 255, 255, 0.1);
  border-top: 1px solid #71717a;
}

/* 道路标线 */
.road-lines {
  position: absolute;
  bottom: 18px;
  left: 0;
  width: 100%;
  height: 4px;
  display: flex;
  justify-content: space-around;
}

.road-line {
  width: 60px;
  height: 4px;
  background: #ffffff;
  border-radius: 2px;
  animation: roadLineMove 3s linear infinite;
}

.road-line:nth-child(1) { animation-delay: 0s; }
.road-line:nth-child(2) { animation-delay: 0.6s; }
.road-line:nth-child(3) { animation-delay: 1.2s; }
.road-line:nth-child(4) { animation-delay: 1.8s; }
.road-line:nth-child(5) { animation-delay: 2.4s; }

@keyframes roadLineMove {
  0% {
    transform: translateX(-100px);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(calc(100vw + 100px));
    opacity: 0;
  }
}

/* 卡通小车样式 */
.car {
  position: absolute;
  bottom: 40px;
  width: 80px;
  height: 40px;
  animation: carMove 8s linear infinite;
}

.car-1 {
  animation-delay: 0s;
}

.car-2 {
  animation-delay: 2.5s;
}

.car-3 {
  animation-delay: 5s;
}

/* 车身 */
.car-body {
  position: relative;
  width: 60px;
  height: 25px;
  background: linear-gradient(135deg, #4ED9F5 0%, #3bc8e6 100%);
  border-radius: 12px 12px 8px 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  margin-left: 10px;
}

.car-2 .car-body {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.car-3 .car-body {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

/* 车顶 */
.car-roof {
  position: absolute;
  top: -8px;
  left: 15px;
  width: 30px;
  height: 15px;
  background: linear-gradient(135deg, #2ab7d5 0%, #1a9cb8 100%);
  border-radius: 8px 8px 4px 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

.car-2 .car-roof {
  background: linear-gradient(135deg, #047857 0%, #065f46 100%);
}

.car-3 .car-roof {
  background: linear-gradient(135deg, #c2410c 0%, #9a3412 100%);
}

/* 车窗 */
.car-window {
  position: absolute;
  top: -6px;
  left: 17px;
  width: 26px;
  height: 10px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 6px 6px 2px 2px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* 车门 */
.car-door {
  position: absolute;
  top: 5px;
  right: 8px;
  width: 2px;
  height: 12px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 1px;
}

/* 车灯 */
.car-headlight {
  position: absolute;
  top: 8px;
  right: -2px;
  width: 4px;
  height: 8px;
  background: radial-gradient(circle, #fbbf24 0%, #f59e0b 100%);
  border-radius: 50%;
  box-shadow: 0 0 6px rgba(251, 191, 36, 0.8);
  animation: headlightBlink 2s ease-in-out infinite alternate;
}

.car-taillight {
  position: absolute;
  top: 10px;
  left: -2px;
  width: 3px;
  height: 6px;
  background: radial-gradient(circle, #ef4444 0%, #dc2626 100%);
  border-radius: 50%;
  box-shadow: 0 0 4px rgba(239, 68, 68, 0.6);
}

@keyframes headlightBlink {
  0% {
    box-shadow: 0 0 6px rgba(251, 191, 36, 0.8);
  }
  100% {
    box-shadow: 0 0 12px rgba(251, 191, 36, 1);
  }
}

/* 尾气效果 */
.car-exhaust {
  position: absolute;
  bottom: 8px;
  left: -10px;
  width: 20px;
  height: 10px;
}

.exhaust-smoke {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(156, 163, 175, 0.6);
  border-radius: 50%;
  animation: smokeFloat 1.5s ease-out infinite;
}

.exhaust-smoke:nth-child(1) {
  animation-delay: 0s;
  left: 0px;
}

.exhaust-smoke:nth-child(2) {
  animation-delay: 0.3s;
  left: 4px;
}

.exhaust-smoke:nth-child(3) {
  animation-delay: 0.6s;
  left: 8px;
}

@keyframes smokeFloat {
  0% {
    transform: translateY(0) scale(0.5);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-15px) scale(1);
    opacity: 0.4;
  }
  100% {
    transform: translateY(-25px) scale(1.5);
    opacity: 0;
  }
}

/* 车轮 */
.car-wheels {
  position: absolute;
  bottom: -8px;
  width: 100%;
  height: 16px;
}

.wheel {
  position: absolute;
  width: 16px;
  height: 16px;
  background: radial-gradient(circle, #1f2937 30%, #374151 70%);
  border-radius: 50%;
  border: 2px solid #111827;
  animation: wheelRotate 0.5s linear infinite;
}

.wheel::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 6px;
  height: 6px;
  background: #6b7280;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.wheel-front {
  right: 8px;
}

.wheel-rear {
  left: 8px;
}

@keyframes carMove {
  0% {
    transform: translateX(-100px);
  }
  100% {
    transform: translateX(calc(100vw + 100px));
  }
}

@keyframes wheelRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 动态背景形状 */
.floating-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.shape {
  position: absolute;
  opacity: 0.15;
  animation: float-shapes 20s infinite linear;
  filter: blur(0.5px);
  transition: all 0.3s ease;
}

.shape:hover {
  opacity: 0.3;
  transform: scale(1.1);
}

.shape-1 {
  width: 80px;
  height: 80px;
  background: linear-gradient(45deg, #4ED9F5, #3bc8e6);
  border-radius: 50%;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #3bc8e6, #2ab7d5);
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  top: 60%;
  left: 80%;
  animation-delay: 5s;
}

.shape-3 {
  width: 100px;
  height: 100px;
  background: linear-gradient(45deg, #4ED9F5, #6ee0f7);
  border-radius: 50%;
  top: 30%;
  left: 70%;
  animation-delay: 10s;
}

.shape-4 {
  width: 40px;
  height: 40px;
  background: linear-gradient(45deg, #8ee7f9, #4ED9F5);
  border-radius: 50%;
  top: 80%;
  left: 20%;
  animation-delay: 15s;
}

.shape-5 {
  width: 70px;
  height: 70px;
  background: linear-gradient(45deg, #aeeefb, #8ee7f9);
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  top: 20%;
  left: 40%;
  animation-delay: 8s;
}

@keyframes float-shapes {
  0% {
    transform: translateY(0px) rotate(0deg) scale(1);
  }
  25% {
    transform: translateY(-15px) rotate(90deg) scale(1.05);
  }
  50% {
    transform: translateY(-30px) rotate(180deg) scale(1.1);
  }
  75% {
    transform: translateY(-15px) rotate(270deg) scale(1.05);
  }
  100% {
    transform: translateY(0px) rotate(360deg) scale(1);
  }
}

/* 优化的卡片样式 */
.advantage-card {
  position: relative;
  height: 250px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  border: 1px solid rgba(78, 217, 245, 0.12);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  overflow: hidden;
  transform: translateY(0) scale(1);
  will-change: transform, box-shadow;
}



.advantage-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(78, 217, 245, 0.06) 0%, rgba(59, 200, 230, 0.03) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.advantage-card:hover,
.card-hovered {
  transform: translateY(-12px) scale(1.03);
  box-shadow:
    0 20px 60px rgba(78, 217, 245, 0.2),
    0 8px 30px rgba(0, 0, 0, 0.1);
  border-color: rgba(78, 217, 245, 0.4);
}

.advantage-card:hover .floating-shapes .shape {
  animation-duration: 10s;
  opacity: 0.25;
}

.advantage-card:hover::before {
  opacity: 1;
}

.card-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2.5rem 2rem;
  text-align: center;
}

/* 图标包装器增强 */
.icon-wrapper {
  position: relative;
  margin-bottom: 1.5rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 图标背景 */
.icon-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(78, 217, 245, 0.1), rgba(78, 217, 245, 0.05));
  border-radius: 50%;
  transition: all 0.4s ease;
  z-index: 1;
}

/* 图标脉冲效果 */
.icon-pulse {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid rgba(78, 217, 245, 0.3);
  border-radius: 50%;
  opacity: 0;
  transform: scale(1);
  animation: iconPulseAnimation 2s ease-in-out infinite;
  z-index: 0;
}

@keyframes iconPulseAnimation {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.3;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

.advantage-card:hover .icon-wrapper {
  transform: scale(1.15) translateY(-4px) rotateY(5deg);
}

.advantage-card:hover .icon-background {
  background: linear-gradient(135deg, rgba(78, 217, 245, 0.2), rgba(78, 217, 245, 0.1));
  transform: scale(1.1);
}

.advantage-card:hover .icon-pulse {
  animation-duration: 1s;
}

.card-icon {
  font-size: 4rem;
  color: #4ED9F5;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 2px 8px rgba(78, 217, 245, 0.2));
  position: relative;
  z-index: 2;
}

.advantage-card:hover .card-icon {
  color: #3bc8e6;
  filter: drop-shadow(0 4px 16px rgba(78, 217, 245, 0.4));
}

.card-title {
  font-family: "Inter", "Noto Sans SC", sans-serif;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  line-height: 1.3;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.card-title::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #4ED9F5, #3bc8e6);
  transition: width 0.3s ease;
  transform: translateX(-50%);
}

.advantage-card:hover .card-title {
  color: #4ED9F5;
  transform: translateY(-1px);
}

.advantage-card:hover .card-title::after {
  width: 60%;
}

.card-description {
  font-family: "Inter", "Noto Sans SC", sans-serif;
  font-size: 1rem;
  font-weight: 400;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  transition: color 0.3s ease;
  letter-spacing: -0.01em;
  opacity: 0.9;
}

.advantage-card:hover .card-description {
  color: #4b5563;
  opacity: 1;
}



/* 响应式优化 */
@media (max-width: 768px) {
  .advantage-card {
    height: 320px;
  }

  .card-content {
    padding: 2rem 1.5rem;
  }

  .card-icon {
    font-size: 3rem;
  }

  .card-title {
    font-size: 1.25rem;
  }

  .card-description {
    font-size: 0.9rem;
  }

  /* 移动端小车优化 */
  .cars-transition-bottom {
    height: 80px;
  }

  .cars-transition-bottom .sky-decorations {
    height: 50px;
  }

  .cars-transition-bottom .cloud {
    transform: scale(0.7);
  }

  .cars-transition-bottom .cloud-1 {
    top: 8px;
  }

  .cars-transition-bottom .cloud-2 {
    top: 15px;
  }

  .cars-transition-bottom .cloud-3 {
    top: 5px;
  }

  .cars-transition-bottom .road-background {
    height: 30px;
  }

  .cars-transition-bottom .road-lines {
    bottom: 13px;
  }

  .cars-transition-bottom .car {
    width: 60px;
    height: 30px;
    bottom: 30px;
    animation: carMove 6s linear infinite;
  }

  .cars-transition-bottom .car-body {
    width: 45px;
    height: 20px;
    margin-left: 8px;
  }

  .cars-transition-bottom .car-roof {
    width: 22px;
    height: 12px;
    left: 11px;
    top: -6px;
  }

  .cars-transition-bottom .car-window {
    width: 20px;
    height: 8px;
    left: 12px;
    top: -5px;
  }

  .cars-transition-bottom .wheel {
    width: 12px;
    height: 12px;
  }

  .cars-transition-bottom .wheel::after {
    width: 4px;
    height: 4px;
  }

  .cars-transition-bottom .wheel-front {
    right: 6px;
  }

  .cars-transition-bottom .wheel-rear {
    left: 6px;
  }

  .cars-transition-bottom .car-headlight {
    width: 3px;
    height: 6px;
  }

  .cars-transition-bottom .car-taillight {
    width: 2px;
    height: 4px;
  }
}

.custom-button {
  position: relative;
  background: linear-gradient(135deg, #4ED9F5 0%, #3bc8e6 100%) !important;
  border: 1px solid transparent !important;
  border-radius: 50px !important;
  height: 56px !important;
  color: #ffffff !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  overflow: hidden !important;
  box-shadow: 0 4px 16px rgba(78, 217, 245, 0.4) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.custom-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.custom-button:hover {
  background: linear-gradient(135deg, #3bc8e6 0%, #2ab7d5 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(78, 217, 245, 0.5) !important;
}

.custom-button:hover::before {
  left: 100%;
}

.custom-button:active {
  transform: translateY(0) !important;
}

/* 卡片进入动画 */
@keyframes cardSlideIn {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.advantage-card {
  animation: cardSlideIn 0.6s ease-out forwards;
}

.advantage-card:nth-child(1) { animation-delay: 0.1s; }
.advantage-card:nth-child(2) { animation-delay: 0.2s; }
.advantage-card:nth-child(3) { animation-delay: 0.3s; }

/* 卡片点击效果 */
.advantage-card:active {
  transform: translateY(-6px) scale(1.01);
  transition: all 0.1s ease;
}

/* 优化移动端体验 */
@media (hover: none) {
  .advantage-card:hover {
    transform: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
  }

  .advantage-card:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
}
</style>

