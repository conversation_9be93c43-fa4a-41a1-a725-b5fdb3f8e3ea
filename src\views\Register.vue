<template>
  <div class="min-h-screen bg-gradient-to-br from-cyan-50 via-sky-100 to-blue-200 flex items-center justify-center relative overflow-hidden animated-bg">
    <!-- 动态背景粒子 -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="floating-particles"></div>
      <div class="gradient-orb orb-1"></div>
      <div class="gradient-orb orb-2"></div>
      <div class="gradient-orb orb-3"></div>
    </div>

    <!-- 主要内容容器 -->
    <div class="w-full max-w-md z-10 px-4 animate-fade-in-up">
      <!-- Logo 区域 -->
      <div class="text-center mb-8">
        <div class="logo-container mb-4">
          <font-awesome-icon icon="fa-shield-alt" class="logo-icon" />
        </div>
        <h1 class="text-3xl font-bold text-gray-800 mb-2 tracking-wide">
          智能车载系统安全卫士
        </h1>
        <p class="text-gray-500 text-sm">创建您的账户，开始安全检测之旅</p>
      </div>

      <!-- 注册卡片 -->
      <div class="register-card">
        <!-- 切换标签 -->
        <div class="flex justify-center mb-8">
          <div class="tab-container">
            <button
              @click="goToLogin"
              :class="['tab-button tab-left', activeTab === 'login' ? 'tab-active' : 'tab-inactive']"
            >
              登录
            </button>
            <button
              @click="activeTab = 'register'"
              :class="['tab-button tab-right', activeTab === 'register' ? 'tab-active' : 'tab-inactive']"
            >
              注册
            </button>
          </div>
        </div>

        <el-form :model="form" :rules="rules" ref="registerForm" class="space-y-5">
          <el-form-item prop="username">
            <el-input
              v-model="form.username"
              placeholder="请输入用户名"
              class="custom-input"
              size="large"
            >
              <template #prefix>
                <el-icon class="input-icon"><User /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="email">
            <el-input
              v-model="form.email"
              placeholder="请输入邮箱"
              class="custom-input"
              size="large"
            >
              <template #prefix>
                <el-icon class="input-icon"><Message /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="password1">
            <el-input
              v-model="form.password1"
              type="password"
              placeholder="请输入密码"
              class="custom-input"
              size="large"
              show-password
            >
              <template #prefix>
                <el-icon class="input-icon"><Lock /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="password2">
            <el-input
              v-model="form.password2"
              type="password"
              placeholder="请再次输入密码"
              class="custom-input"
              size="large"
              show-password
            >
              <template #prefix>
                <el-icon class="input-icon"><Lock /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <!-- 用户协议 -->
          <div class="flex items-center mb-6">
            <el-checkbox v-model="agreeTerms" class="terms-checkbox">
              我已阅读并同意
            </el-checkbox>
            <a href="#" class="terms-link">《用户协议》</a>
            <span class="text-gray-500 mx-1">和</span>
            <a href="#" class="terms-link">《隐私政策》</a>
          </div>

          <el-form-item>
            <el-button
              type="primary"
              class="register-button"
              @click="handleRegister"
              :loading="registerLoading"
              :disabled="!agreeTerms"
              size="large"
            >
              {{ registerLoading ? '注册中...' : '创建账户' }}
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 已有账户提示 -->
        <div class="login-prompt">
          <span class="text-gray-500">已有账户？</span>
          <button @click="goToLogin" class="login-link">立即登录</button>
        </div>
      </div>
    </div>
  </div>
</template>


<script>
import { ref } from "vue";
import { useRouter } from "vue-router";
import axios from "axios";
import { ElMessage } from "element-plus";
import { Lock, Message, User } from "@element-plus/icons-vue";
import qs from "qs";

export default {
  components: {
    User,
    Message,
    Lock,
  },
  data() {
    return {
      activeTab: "register",
      registerLoading: false,
      agreeTerms: false,
      form: {
        username: "",
        email: "",
        password1: "",
        password2: "",
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
        ],
        password1: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于 6 位', trigger: 'blur' }
        ],
        password2: [
          { required: true, message: '请再次输入密码', trigger: 'blur' },
          { validator: this.validatePassword2, trigger: 'blur' }
        ]
      }
    };
  },
  methods: {
    validatePassword2(rule, value, callback) {
      if (value === '') {
        callback(new Error('请再次输入密码'));
      } else if (value !== this.form.password1) {
        callback(new Error('两次输入密码不一致!'));
      } else {
        callback();
      }
    },

    handleRegister() {
      if (!this.agreeTerms) {
        this.$message.warning('请先同意用户协议和隐私政策');
        return;
      }

      this.$refs.registerForm.validate((valid) => {
        if (valid) {
          this.registerLoading = true;

          axios.post("/api/register",
            qs.stringify({
              username: this.form.username,
              email: this.form.email,
              password: this.form.password1,
              confirmPassword: this.form.password2,
            })
          )
          .then((response) => {
            if (response.data.msg === "success") {
              this.$message.success("注册成功！即将跳转到登录页面");
              setTimeout(() => {
                this.$router.push("/login");
              }, 1500);
            } else {
              this.$message.error(response.data.msg);
            }
          })
          .catch((error) => {
            this.$message.error(error.response?.data?.msg || "注册失败，请稍后重试");
          })
          .finally(() => {
            this.registerLoading = false;
          });
        }
      });
    },

    goToLogin() {
      this.$router.push("/login");
    },
  },
};
</script>

<style scoped>
/* 动画效果 */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes gradient-shift {
  0%, 100% { transform: translateX(-50%) translateY(-50%) rotate(0deg); }
  50% { transform: translateX(-50%) translateY(-50%) rotate(180deg); }
}

@keyframes bg-animation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes wave-animation {
  0%, 100% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out;
}

/* 背景动画效果 */
.animated-bg {
  background: linear-gradient(-45deg,
    #e0f2fe,
    #bae6fd,
    #7dd3fc,
    #38bdf8,
    #0ea5e9);
  background-size: 400% 400%;
  animation: bg-animation 15s ease infinite;
  position: relative;
}

.animated-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    rgba(78, 217, 245, 0.1) 0%,
    rgba(56, 189, 248, 0.05) 25%,
    rgba(14, 165, 233, 0.08) 50%,
    rgba(2, 132, 199, 0.03) 75%,
    rgba(3, 105, 161, 0.1) 100%);
  animation: wave-animation 20s ease-in-out infinite;
  pointer-events: none;
}

/* 背景效果 */
.floating-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(78, 217, 245, 0.4) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(56, 189, 248, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(14, 165, 233, 0.3) 0%, transparent 50%);
  animation: float 6s ease-in-out infinite;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  animation: gradient-shift 8s ease-in-out infinite;
}

.orb-1 {
  width: 200px;
  height: 200px;
  background: linear-gradient(45deg, #4ED9F5 0%, #38bdf8 100%);
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.orb-2 {
  width: 150px;
  height: 150px;
  background: linear-gradient(45deg, #0ea5e9 0%, #0284c7 100%);
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.orb-3 {
  width: 100px;
  height: 100px;
  background: linear-gradient(45deg, #7dd3fc 0%, #0ea5e9 100%);
  bottom: 20%;
  left: 60%;
  animation-delay: 4s;
}

/* Logo 样式 */
.logo-container {
  display: inline-block;
  animation: float 3s ease-in-out infinite;
}

.logo-icon {
  font-size: 3rem;
  color: #4ED9F5;
  filter: drop-shadow(0 4px 8px rgba(78, 217, 245, 0.3));
}

/* 注册卡片 */
.register-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 2rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.register-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    0 4px 12px rgba(0, 0, 0, 0.08);
}

/* 标签切换 */
.tab-container {
  display: flex;
  background: rgba(243, 244, 246, 0.8);
  border-radius: 12px;
  padding: 4px;
  backdrop-filter: blur(10px);
}

.tab-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
}

.tab-active {
  background: linear-gradient(135deg, #4ED9F5 0%, #3bc8e6 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(78, 217, 245, 0.4);
}

.tab-inactive {
  background: transparent;
  color: #6b7280;
}

.tab-inactive:hover {
  background: rgba(255, 255, 255, 0.8);
  color: #374151;
}

/* 输入框样式 */
.custom-input :deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(229, 231, 235, 0.8);
  border-radius: 16px;
  height: 56px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.custom-input :deep(.el-input__wrapper:hover) {
  border-color: rgba(78, 217, 245, 0.5);
  background: rgba(255, 255, 255, 0.95);
}

.custom-input :deep(.el-input__wrapper.is-focus) {
  border-color: #4ED9F5;
  box-shadow: 0 0 0 4px rgba(78, 217, 245, 0.1);
  background: rgba(255, 255, 255, 1);
}

.custom-input :deep(.el-input__inner) {
  color: #374151;
  font-size: 16px;
  font-weight: 500;
}

.input-icon {
  color: #9ca3af;
  font-size: 18px;
  transition: color 0.3s ease;
}

.custom-input :deep(.el-input__wrapper.is-focus) .input-icon {
  color: #4ED9F5;
}

/* 用户协议样式 */
.terms-checkbox :deep(.el-checkbox__label) {
  color: #6b7280;
  font-size: 14px;
}

.terms-checkbox :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #4ED9F5;
  border-color: #4ED9F5;
}

.terms-link {
  color: #4ED9F5;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.terms-link:hover {
  color: #3bc8e6;
  text-decoration: underline;
}

/* 注册按钮 */
.register-button {
  width: 100%;
  height: 56px;
  background: linear-gradient(135deg, #4ED9F5 0%, #38bdf8 50%, #0ea5e9 100%);
  border: none;
  border-radius: 16px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 16px rgba(78, 217, 245, 0.4),
    0 2px 8px rgba(78, 217, 245, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.register-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent);
  transition: left 0.6s ease;
}

.register-button:hover:not(:disabled)::before {
  left: 100%;
}

.register-button:hover:not(:disabled) {
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 12px 32px rgba(78, 217, 245, 0.6),
    0 6px 16px rgba(78, 217, 245, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  background: linear-gradient(135deg, #3bc8e6 0%, #0ea5e9 50%, #0284c7 100%);
}

.register-button:active {
  transform: translateY(-1px) scale(1.01);
  transition: all 0.1s ease;
}

.register-button:focus {
  outline: none;
  box-shadow:
    0 8px 24px rgba(78, 217, 245, 0.5),
    0 0 0 4px rgba(78, 217, 245, 0.2);
}

.register-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #9ca3af;
  transform: none;
  box-shadow: none;
}

/* 登录提示 */
.login-prompt {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(229, 231, 235, 0.8);
}

.login-link {
  color: #4ED9F5;
  background: none;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.3s ease;
  margin-left: 0.5rem;
}

.login-link:hover {
  color: #3bc8e6;
  text-decoration: underline;
}

/* 错误信息样式 */
:deep(.el-form-item__error) {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .register-card {
    padding: 1.5rem;
    margin: 1rem;
  }

  .tab-button {
    padding: 10px 20px;
    font-size: 13px;
  }
}
</style>

